/**
 * Read a parquet file using the backend API
 * @param {File} file - The parquet file to read
 * @returns {Promise<Array>} - Array of row objects
 */
export async function readParquetFile(file) {
  try {
    const formData = new FormData();
    formData.append('file', file);
    
    const response = await fetch('http://localhost:5001/api/upload', {
      method: 'POST',
      body: formData,
    });
    
    if (!response.ok) {
      const errorData = await response.json();
      throw new Error(errorData.error || 'Failed to upload file');
    }
    
    const result = await response.json();
    
    return { 
      rows: result.data, 
      schema: { fields: result.columns.map(name => ({ name })) },
      aggregated: result.aggregated || false
    };
  } catch (error) {
    console.error('Error reading parquet file:', error);
    throw new Error(`Failed to read parquet file: ${error.message}`);
  }
}

/**
 * Process Greeks data for heatmap visualization
 * @param {Array} rows - Raw data rows from parquet file
 * @returns {Object} - Processed data with strikes, dates/times, and values
 */
export function processGreeksData(rows) {
  if (!rows || rows.length === 0) {
    return { strikes: [], columns: [], data: {} };
  }
  
  // Determine which columns are numeric (potential exposure metrics)
  const firstRow = rows[0];
  const numericColumns = Object.keys(firstRow).filter(key => {
    const value = firstRow[key];
    return typeof value === 'number' &&
           !['volume', 'open', 'close', 'high', 'low', 'window_start', 'transactions', 'time_to_expiry', 'strike'].includes(key);
  });
  
  // Determine the time dimension column (prefer 'date' over 'time' or 'datetime')
  let timeColumn = 'date';
  if (!firstRow.hasOwnProperty('date')) {
    if (firstRow.hasOwnProperty('datetime')) {
      timeColumn = 'datetime';
    } else if (firstRow.hasOwnProperty('time')) {
      timeColumn = 'time';
    }
  }
  
  // Group by strike and time dimension
  const dataMap = new Map();
  const strikesSet = new Set();
  const timesSet = new Set();
  
  rows.forEach(row => {
    const strike = row.strike;
    let time = row[timeColumn] || 'N/A';
    
    // Convert time to string if needed
    if (time && typeof time === 'object' && time.toString) {
      time = time.toString();
    }
    
    // Extract just the date part if it's a datetime string
    if (typeof time === 'string' && time.includes('T')) {
      time = time.split('T')[0];
    }
    
    if (strike !== undefined && strike !== null) {
      strikesSet.add(strike);
      timesSet.add(time);
      
      const key = `${strike}_${time}`;
      if (!dataMap.has(key)) {
        dataMap.set(key, { strike, time });
      }
      
      // Add all numeric columns
      numericColumns.forEach(col => {
        if (row[col] !== undefined && row[col] !== null) {
          dataMap.get(key)[col] = row[col];
        }
      });
    }
  });
  
  // Sort strikes and times
  const strikes = Array.from(strikesSet).sort((a, b) => b - a); // Descending order
  const times = Array.from(timesSet).sort();
  
  // Create column definitions for exposure metrics (prioritize GEX, VEX, CEX)
  const exposureColumns = ['gex', 'vex', 'cex', 'delta', 'gamma', 'theta', 'vega', 'rho'].filter(g =>
    numericColumns.includes(g)
  );
  
  return {
    strikes,
    times,
    exposureColumns,
    dataMap,
    numericColumns,
    timeColumn
  };
}

/**
 * Get color for a cell based on its value
 * @param {number} value - The cell value
 * @param {string} column - The column name
 * @param {number} min - Minimum value in the dataset
 * @param {number} max - Maximum value in the dataset
 * @returns {string} - CSS color string
 */
export function getCellColor(value, column, min, max) {
  if (value === undefined || value === null || isNaN(value)) {
    return 'rgb(71, 85, 105)'; // slate-600
  }
  
  // Normalize value to -1 to 1 range
  const range = Math.max(Math.abs(min), Math.abs(max));
  if (range === 0) return 'rgb(71, 85, 105)';
  
  const normalized = value / range;
  
  // Color mapping: negative = red, positive = green, near zero = light
  if (normalized > 0) {
    // Positive: green shades
    const intensity = Math.min(Math.abs(normalized), 1);
    const r = Math.round(220 - intensity * 120); // 220 -> 100
    const g = Math.round(252 - intensity * 32);  // 252 -> 220
    const b = Math.round(220 - intensity * 120); // 220 -> 100
    return `rgb(${r}, ${g}, ${b})`;
  } else if (normalized < 0) {
    // Negative: red shades
    const intensity = Math.min(Math.abs(normalized), 1);
    const r = Math.round(254 - intensity * 24);  // 254 -> 230
    const g = Math.round(226 - intensity * 126); // 226 -> 100
    const b = Math.round(226 - intensity * 126); // 226 -> 100
    return `rgb(${r}, ${g}, ${b})`;
  } else {
    // Near zero: light gray/blue
    return 'rgb(203, 213, 225)'; // slate-300
  }
}

