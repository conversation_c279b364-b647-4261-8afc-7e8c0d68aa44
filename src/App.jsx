import { useState, useRef, useMemo } from 'react'
import { Button } from '@/components/ui/button.jsx'
import { Upload, Download } from 'lucide-react'
import { readParquetFile, processGreeksData, getCellColor } from '@/lib/parquetReader'
import './App.css'

function App() {
  const [data, setData] = useState(null)
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState(null)
  const [fileName, setFileName] = useState('')
  const [selectedExposure, setSelectedExposure] = useState('gex')
  const fileInputRef = useRef(null)

  const handleFileUpload = async (event) => {
    const file = event.target.files?.[0]
    if (!file) return

    setLoading(true)
    setError(null)
    setFileName(file.name)

    try {
      const { rows } = await readParquetFile(file)
      const processedData = processGreeksData(rows)
      setData(processedData)

      // Set default selected exposure to first available
      if (processedData.exposureColumns && processedData.exposureColumns.length > 0) {
        setSelectedExposure(processedData.exposureColumns[0])
      }
    } catch (err) {
      setError(err.message)
      console.error('Error processing file:', err)
    } finally {
      setLoading(false)
    }
  }

  const handleButtonClick = () => {
    fileInputRef.current?.click()
  }

  // Calculate min/max for color scaling
  const { minValue, maxValue } = useMemo(() => {
    if (!data || !selectedExposure) return { minValue: 0, maxValue: 0 }

    let min = Infinity
    let max = -Infinity

    data.dataMap.forEach(row => {
      const value = row[selectedExposure]
      if (value !== undefined && value !== null && !isNaN(value)) {
        min = Math.min(min, value)
        max = Math.max(max, value)
      }
    })

    return {
      minValue: min === Infinity ? 0 : min,
      maxValue: max === -Infinity ? 0 : max
    }
  }, [data, selectedExposure])

  // Get limited time samples for display (show every Nth time to avoid overcrowding)
  const displayTimes = useMemo(() => {
    if (!data || !data.times) return []
    const times = data.times
    if (times.length <= 10) return times
    
    // Sample times to show max 10 columns
    const step = Math.ceil(times.length / 10)
    return times.filter((_, idx) => idx % step === 0)
  }, [data])

  const formatValue = (value) => {
    if (value === undefined || value === null || isNaN(value)) return '-'
    if (Math.abs(value) < 0.01 && value !== 0) {
      return value.toExponential(2)
    }
    return value.toFixed(2)
  }

  const formatTime = (time) => {
    if (!time || time === 'N/A') return 'N/A'
    if (typeof time === 'string') {
      // If it looks like a date (YYYY-MM-DD), return as is
      if (time.match(/^\d{4}-\d{2}-\d{2}$/)) {
        return time
      }
      // Extract time portion if it's a datetime string
      const match = time.match(/(\d{2}:\d{2}:\d{2})/)
      return match ? match[1].substring(0, 5) : time
    }
    return String(time)
  }

  return (
    <div className="min-h-screen bg-slate-50 p-8">
      <div className="max-w-[95vw] mx-auto">
        {/* Header */}
        <div className="mb-8">
          <h1 className="text-4xl font-bold text-slate-900 mb-2">
            Options Exposure Heatmap Viewer
          </h1>
          <p className="text-slate-600">
            Upload a parquet file to visualize options exposure data (GEX, VEX, CEX) in a heatmap format
          </p>
        </div>

        {/* File Upload Section */}
        <div className="bg-white rounded-lg shadow-sm border border-slate-200 p-6 mb-6">
          <div className="flex items-center gap-4">
            <input
              ref={fileInputRef}
              type="file"
              accept=".parquet"
              onChange={handleFileUpload}
              className="hidden"
            />
            <Button 
              onClick={handleButtonClick}
              disabled={loading}
              className="flex items-center gap-2"
            >
              <Upload className="w-4 h-4" />
              {loading ? 'Loading...' : 'Upload Parquet File'}
            </Button>
            {fileName && (
              <span className="text-sm text-slate-600">
                Loaded: <span className="font-medium">{fileName}</span>
              </span>
            )}
          </div>
          {error && (
            <div className="mt-4 p-3 bg-red-50 border border-red-200 rounded text-red-700 text-sm">
              {error}
            </div>
          )}
        </div>

        {/* Exposure Selector */}
        {data && data.exposureColumns && (
          <div className="bg-white rounded-lg shadow-sm border border-slate-200 p-6 mb-6">
            <label className="block text-sm font-medium text-slate-700 mb-3">
              Select Exposure to Display:
            </label>
            <div className="flex gap-2 flex-wrap">
              {data.exposureColumns.map(exposure => (
                <Button
                  key={exposure}
                  onClick={() => setSelectedExposure(exposure)}
                  variant={selectedExposure === exposure ? 'default' : 'outline'}
                  className="capitalize"
                >
                  {exposure.toUpperCase()}
                </Button>
              ))}
            </div>
          </div>
        )}

        {/* Heatmap Table */}
        {data && data.strikes && data.strikes.length > 0 && (
          <div className="bg-white rounded-lg shadow-sm border border-slate-200 p-6 overflow-auto">
            <div className="mb-4 flex justify-between items-center">
              <h2 className="text-xl font-semibold text-slate-900">
                {selectedExposure.toUpperCase()} Heatmap
              </h2>
              <div className="text-sm text-slate-600">
                Range: {formatValue(minValue)} to {formatValue(maxValue)}
              </div>
            </div>
            
            <div className="overflow-x-auto">
              <table className="w-full border-collapse">
                <thead>
                  <tr>
                    <th className="sticky left-0 z-10 bg-slate-700 text-white px-4 py-3 text-left font-semibold border border-slate-600">
                      Strike
                    </th>
                    {displayTimes.map((time, idx) => (
                      <th 
                        key={idx}
                        className="bg-slate-700 text-white px-4 py-3 text-center font-semibold border border-slate-600 min-w-[120px]"
                      >
                        <div className="text-xs">{formatTime(time)}</div>
                        <div className="text-xs font-normal mt-1 opacity-80">
                          {selectedExposure.toUpperCase()}
                        </div>
                      </th>
                    ))}
                  </tr>
                </thead>
                <tbody>
                  {data.strikes.map((strike, rowIdx) => (
                    <tr key={rowIdx} className="hover:bg-slate-50 transition-colors">
                      <td className="sticky left-0 z-10 bg-slate-700 text-white px-4 py-3 font-semibold border border-slate-600">
                        {strike}
                      </td>
                      {displayTimes.map((time, colIdx) => {
                        const key = `${strike}_${time}`
                        const cellData = data.dataMap.get(key)
                        const value = cellData?.[selectedExposure]
                        const bgColor = getCellColor(value, selectedExposure, minValue, maxValue)
                        
                        return (
                          <td
                            key={colIdx}
                            className="px-4 py-3 text-center border border-slate-300 font-mono text-sm transition-all hover:scale-105 hover:shadow-md cursor-default"
                            style={{ backgroundColor: bgColor }}
                            title={`Strike: ${strike}, Time: ${formatTime(time)}, ${selectedExposure.toUpperCase()}: ${formatValue(value)}`}
                          >
                            {formatValue(value)}
                          </td>
                        )
                      })}
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>

            <div className="mt-4 text-sm text-slate-600">
              Showing {data.strikes.length} strikes × {displayTimes.length} time periods
              {data.times.length > displayTimes.length && (
                <span className="ml-2 text-slate-500">
                  (sampled from {data.times.length} total time periods)
                </span>
              )}
            </div>
          </div>
        )}

        {/* Empty State */}
        {!data && !loading && (
          <div className="bg-white rounded-lg shadow-sm border border-slate-200 p-12 text-center">
            <Upload className="w-16 h-16 text-slate-300 mx-auto mb-4" />
            <h3 className="text-lg font-medium text-slate-900 mb-2">
              No Data Loaded
            </h3>
            <p className="text-slate-600">
              Upload a parquet file to get started
            </p>
          </div>
        )}
      </div>
    </div>
  )
}

export default App

