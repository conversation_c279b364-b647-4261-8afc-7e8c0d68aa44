# Greeks Heatmap Viewer

A web application for visualizing options Greeks data from parquet files in an interactive heatmap format with automatic daily aggregation.

## Features

- **Dynamic File Upload**: Upload any parquet file containing options Greeks data
- **Automatic Daily Aggregation**: Intraday data is automatically aggregated to daily averages
- **Interactive Heatmap**: Color-coded visualization with:
  - Green shades for positive values
  - Red shades for negative values
  - Gray for missing/null values
- **Multiple Greeks Support**: Switch between Delta, Gamma, Theta, Vega, and Rho
- **Responsive Design**: Modern UI with hover effects and smooth transitions
- **Smart Data Processing**: Handles time series data and organizes by strikes and dates

## Architecture

The application consists of two main components:

### Frontend (React + Vite)
- **Technology Stack**: React 19, Vite, Tailwind CSS, shadcn/ui
- **Location**: `/src` directory
- **Key Components**:
  - `App.jsx`: Main application component with file upload and heatmap display
  - `lib/parquetReader.js`: Data processing and color mapping utilities

### Backend (Flask)
- **Technology Stack**: Python 3.11, Flask, pandas, pyarrow
- **Location**: `/backend` directory
- **Key Features**:
  - Parquet file parsing
  - **Automatic daily aggregation** of intraday data
  - Data serialization for browser consumption
  - CORS enabled for local development

## Installation & Setup

### Prerequisites
- Node.js 22+ with pnpm
- Python 3.11+

### Frontend Setup
```bash
cd greeks-heatmap-viewer
pnpm install
```

### Backend Setup
```bash
cd backend
pip3 install -r requirements.txt
```

## Running the Application

### Start Backend Server
```bash
cd backend
python3.11 app.py
```
The backend will run on `http://localhost:5000`

### Start Frontend Development Server
```bash
cd greeks-heatmap-viewer
pnpm run dev --host
```
The frontend will run on `http://localhost:5173`

### Access the Application
Open your browser and navigate to `http://localhost:5173`

## Usage

1. **Upload a Parquet File**:
   - Click the "Upload Parquet File" button
   - Select a parquet file containing options data with columns like:
     - `strike`: Option strike prices
     - `date` or `datetime`: Date/time information
     - `delta`, `gamma`, `theta`, `vega`, `rho`: Greek values

2. **Automatic Aggregation**:
   - If your file contains intraday data (multiple time points per day), the backend automatically aggregates it to daily averages
   - Each strike will have one value per date (mean of all intraday measurements)

3. **View the Heatmap**:
   - The application displays data organized by strikes (rows) and dates (columns)
   - Color intensity indicates value magnitude

4. **Switch Between Greeks**:
   - Use the Greek selector buttons to view different metrics
   - The heatmap updates instantly with new color scaling

## Data Format

The application expects parquet files with the following structure:

| Column | Type | Description |
|--------|------|-------------|
| strike | float | Option strike price |
| date or datetime | date/datetime | Date or datetime information |
| delta | float | Delta Greek value |
| gamma | float | Gamma Greek value |
| theta | float | Theta Greek value |
| vega | float | Vega Greek value |
| rho | float | Rho Greek value |

**Note**: If your data includes a `time` column with intraday timestamps, the backend will automatically group by date and aggregate values using the mean.

## Daily Aggregation Logic

When processing intraday data:

1. **Detection**: Backend detects if data contains `date` or `datetime` columns
2. **Grouping**: Data is grouped by `date` and `strike` (and `option_type` if present)
3. **Aggregation**: Greek values are averaged across all intraday measurements
4. **Result**: One value per strike per date

**Example**:
- Input: 10 measurements for strike 4000 on 2022-12-28 (every hour)
- Output: 1 aggregated value for strike 4000 on 2022-12-28 (mean of 10 values)

## Color Coding

The heatmap uses a diverging color scheme:

- **Positive Values**: Light green → Dark green (intensity increases with magnitude)
- **Negative Values**: Light red → Dark red (intensity increases with magnitude)
- **Zero/Near-Zero**: Light gray/blue
- **Missing Data**: Dark slate gray

Color intensity is normalized based on the min/max values in the current Greek being displayed.

## Project Structure

```
greeks-heatmap-viewer/
├── backend/
│   ├── app.py              # Flask backend server with aggregation logic
│   └── requirements.txt    # Python dependencies
├── src/
│   ├── components/
│   │   └── ui/             # shadcn/ui components
│   ├── lib/
│   │   └── parquetReader.js # Data processing utilities
│   ├── App.jsx             # Main React component
│   ├── App.css             # Tailwind styles
│   └── main.jsx            # React entry point
├── public/                 # Static assets
├── index.html              # HTML entry point
├── package.json            # Node dependencies
├── README.md               # This file
└── QUICKSTART.md           # Quick start guide
```

## Development

### Building for Production
```bash
pnpm run build
```

### Linting
```bash
pnpm run lint
```

## Technical Details

### Data Processing Pipeline

1. **File Upload**: User selects a parquet file through the browser
2. **Backend Processing**: 
   - Flask receives the file via multipart/form-data
   - pandas reads the parquet file
   - **Aggregation**: If date/datetime columns exist, data is grouped by date and strike, then averaged
   - Time objects are converted to strings for JSON serialization
   - Data is returned as JSON array
3. **Frontend Processing**:
   - Data is grouped by strike and date
   - Numeric columns are identified as potential Greeks
   - Min/max values are calculated for color scaling
4. **Rendering**:
   - React renders the heatmap table
   - Each cell is colored based on its normalized value
   - Hover effects show detailed information

### Aggregation Benefits

- **Cleaner Visualization**: One column per date instead of dozens of intraday columns
- **Better Overview**: Easier to spot patterns across dates
- **Reduced Clutter**: More readable heatmap for multi-day datasets
- **Accurate Representation**: Mean values provide a fair daily summary

### Performance Considerations

- **Efficient Aggregation**: pandas groupby operations are optimized
- **Backend Processing**: Heavy computation done server-side
- **Minimal Frontend Load**: React receives pre-processed data

## Limitations

- Large parquet files (>100MB) may take longer to process
- Aggregation uses mean (average) - other methods not currently supported
- Very sparse datasets may show mostly gray cells

## Future Enhancements

Potential improvements for future versions:

- [ ] Add zoom/pan functionality for large datasets
- [ ] Export heatmap as image
- [ ] Support for custom color schemes
- [ ] Filtering by strike range or date range
- [ ] Multiple file comparison view
- [ ] Configurable aggregation methods (mean, median, last, etc.)
- [ ] Multi-date support with date range selector
- [ ] Deployment-ready production build

## Example Use Cases

1. **Intraday Options Analysis**: Upload minute-by-minute Greeks data, view daily aggregates
2. **Historical Greeks Tracking**: Compare daily Greek values across multiple dates
3. **Strike Analysis**: Identify patterns in Greek values across strike prices
4. **Risk Management**: Monitor theta decay and gamma exposure at a glance

## License

This project is provided as-is for educational and analytical purposes.

## Support

For issues or questions, please refer to the project documentation or contact the development team.

