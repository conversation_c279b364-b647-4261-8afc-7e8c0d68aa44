# Quick Start Guide

## Get Started in 3 Steps

### Step 1: Install Dependencies

**Backend:**
```bash
cd backend
pip3 install -r requirements.txt
```

**Frontend:**
```bash
cd greeks-heatmap-viewer
pnpm install
```

### Step 2: Start Both Servers

**Terminal 1 - Backend:**
```bash
cd backend
python3.11 app.py
```
✓ Backend running on http://localhost:5000

**Terminal 2 - Frontend:**
```bash
cd greeks-heatmap-viewer
pnpm run dev --host
```
✓ Frontend running on http://localhost:5173

### Step 3: Open and Use

1. Open browser to **http://localhost:5173**
2. Click **"Upload Parquet File"**
3. Select your parquet file (e.g., `spx_intra_greeks_2022-12-28.parquet`)
4. View the heatmap with **daily aggregated data**!
5. Click Greek buttons (Delta, Gamma, Theta, Vega, Rho) to switch views

## What Happens to Your Data?

### Automatic Daily Aggregation

If your parquet file contains **intraday data** (multiple time points per day):
- ✅ Backend automatically detects date/datetime columns
- ✅ Groups data by date and strike
- ✅ Calculates mean (average) for each Greek
- ✅ Displays one clean column per date

**Example:**
```
Input:  Strike 4000 @ 09:30, 10:00, 10:30, ... (10 rows)
Output: Strike 4000 @ 2022-12-28 (1 row, averaged)
```

### Result

Instead of seeing this:
```
Strike | 09:30 | 10:00 | 10:30 | 11:00 | ... (many columns)
```

You see this:
```
Strike | 2022-12-28
```

Much cleaner and easier to read! 🎉

## Expected File Format

Your parquet file should have columns like:
- `strike` - Strike prices
- `date` or `datetime` - Date/time information  
- `delta`, `gamma`, `theta`, `vega`, `rho` - Greek values

The application will automatically detect and process these columns.

## Troubleshooting

**Backend won't start?**
- Make sure port 5000 is not in use
- Check that all Python packages are installed: `pip3 list | grep -E "flask|pandas|pyarrow"`

**Frontend won't start?**
- Make sure port 5173 is not in use
- Try: `pnpm install` again

**File upload fails?**
- Ensure backend is running on port 5000
- Check browser console for error messages
- Verify parquet file is valid

**No data showing?**
- Check that your parquet file has `strike` column
- Verify Greek columns (delta, gamma, etc.) exist
- Look for error messages in the red error box

**Aggregation not working?**
- Ensure your file has a `date` or `datetime` column
- Check backend logs for aggregation confirmation

## Tips

- **Large files**: May take 5-10 seconds to process and aggregate
- **Intraday data**: Automatically aggregated to daily - no action needed
- **Sparse data**: Will show many gray cells (missing values)
- **Hover over cells**: See exact values in tooltip
- **Scroll down**: View more strikes in the table
- **Multiple dates**: If your file has multiple dates, you'll see multiple columns

## What's Different from the Original?

The updated version now includes:
- ✅ **Automatic daily aggregation** of intraday data
- ✅ **Cleaner visualization** with one column per date
- ✅ **Better performance** for large intraday datasets
- ✅ **Easier pattern recognition** across dates

