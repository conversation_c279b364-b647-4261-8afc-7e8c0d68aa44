from flask import Flask, request, jsonify
from flask_cors import CORS
import pandas as pd
import numpy as np
import io
import json
import datetime

app = Flask(__name__)
CORS(app)

def calculate_exposures(df):
    """
    Calculate GEX, VEX, CEX from traditional Greeks if they don't exist

    GEX (Gamma Exposure) = Gamma * Volume * Underlying_Price^2 * 0.01 (scaled for display)
    VEX (Vega Exposure) = Vega * Volume * 10 (scaled for display)
    CEX (Call Exposure) = Delta * Volume * Underlying_Price * 0.01 (scaled for display)

    Scaling factors chosen to produce reasonable display values
    """
    # Check if exposure columns already exist
    has_gex = 'gex' in df.columns
    has_vex = 'vex' in df.columns
    has_cex = 'cex' in df.columns

    if has_gex and has_vex and has_cex:
        return df  # All exposures already exist

    # Get required columns for calculations
    has_gamma = 'gamma' in df.columns
    has_vega = 'vega' in df.columns
    has_delta = 'delta' in df.columns
    has_volume = 'volume' in df.columns
    has_underlying = 'underlying_close' in df.columns
    has_strike = 'strike' in df.columns
    has_option_type = 'option_type' in df.columns

    # Use default values if not available
    default_volume = 100  # Default contract volume
    default_underlying = 4000  # Default underlying price (SPX-like)

    if not has_gex and has_gamma:
        volume = df['volume'] if has_volume else default_volume
        underlying = df['underlying_close'] if has_underlying else default_underlying
        # GEX = Gamma * Volume * Underlying^2 * 0.01 (scaled for reasonable display values)
        df['gex'] = df['gamma'] * volume * (underlying ** 2) * 0.01

    if not has_vex and has_vega:
        volume = df['volume'] if has_volume else default_volume
        # VEX = Vega * Volume * 10 (scaled for reasonable display values)
        df['vex'] = df['vega'] * volume * 10

    if not has_cex and has_delta:
        volume = df['volume'] if has_volume else default_volume
        underlying = df['underlying_close'] if has_underlying else default_underlying

        # CEX calculation depends on option type
        if has_option_type:
            # For calls: positive delta exposure, for puts: negative delta exposure
            call_multiplier = df['option_type'].apply(lambda x: 1 if str(x).upper().startswith('C') else -1)
            df['cex'] = df['delta'] * volume * underlying * call_multiplier * 0.01
        else:
            # If no option type, assume mixed (use delta as-is)
            df['cex'] = df['delta'] * volume * underlying * 0.01

    return df

@app.route('/api/upload', methods=['POST'])
def upload_parquet():
    try:
        if 'file' not in request.files:
            return jsonify({'error': 'No file provided'}), 400
        
        file = request.files['file']
        if file.filename == '':
            return jsonify({'error': 'No file selected'}), 400
        
        # Read the parquet file
        df = pd.read_parquet(io.BytesIO(file.read()))

        # Calculate GEX, VEX, CEX if they don't exist
        df = calculate_exposures(df)
        
        # Identify date column (could be 'date', 'datetime', or extract from datetime)
        date_col = None
        if 'date' in df.columns:
            date_col = 'date'
        elif 'datetime' in df.columns:
            # Extract date from datetime
            df['date'] = pd.to_datetime(df['datetime']).dt.date
            date_col = 'date'
        
        # Identify strike column
        strike_col = 'strike' if 'strike' in df.columns else None
        
        if date_col and strike_col:
            # Identify exposure columns to aggregate (GEX, VEX, CEX)
            exposure_columns = ['gex', 'vex', 'cex', 'delta', 'gamma', 'theta', 'vega', 'rho', 'implied_vol']
            available_exposures = [col for col in exposure_columns if col in df.columns]
            
            # Group by date and strike, then aggregate (mean for Greeks)
            group_cols = [date_col, strike_col]
            
            # Add option_type if it exists (to separate calls and puts)
            if 'option_type' in df.columns:
                group_cols.append('option_type')
            
            agg_dict = {col: 'mean' for col in available_exposures}
            
            # Perform aggregation
            df_agg = df.groupby(group_cols, as_index=False).agg(agg_dict)
            
            # Convert date to string if needed
            if date_col in df_agg.columns:
                df_agg[date_col] = df_agg[date_col].astype(str)
        else:
            # No aggregation needed, just convert time objects
            df_agg = df.copy()
            for col in df_agg.columns:
                if df_agg[col].dtype == 'object':
                    if len(df_agg) > 0 and isinstance(df_agg[col].iloc[0], datetime.time):
                        df_agg[col] = df_agg[col].astype(str)
        
        # Convert to JSON-serializable format
        data = df_agg.to_dict('records')
        
        # Get column info
        columns = list(df_agg.columns)
        dtypes = {col: str(dtype) for col, dtype in df_agg.dtypes.items()}
        
        return jsonify({
            'success': True,
            'data': data,
            'columns': columns,
            'dtypes': dtypes,
            'row_count': len(df_agg),
            'column_count': len(columns),
            'aggregated': date_col is not None and strike_col is not None
        })
    
    except Exception as e:
        import traceback
        return jsonify({'error': str(e), 'traceback': traceback.format_exc()}), 500

@app.route('/api/health', methods=['GET'])
def health():
    return jsonify({'status': 'ok'})

if __name__ == '__main__':
    app.run(host='0.0.0.0', port=5000, debug=True)

