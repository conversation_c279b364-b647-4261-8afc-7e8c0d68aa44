#!/usr/bin/env python3
"""
Create sample parquet file with GEX, VEX, CEX exposure data
"""
import pandas as pd
import numpy as np
from datetime import datetime, date

# Set random seed for reproducible data
np.random.seed(42)

# Define strike prices (similar to SPX options)
strikes = np.arange(6500, 7000, 25)  # Strike range from 6500 to 6975

# Create sample data for 2025-10-14
sample_date = date(2025, 10, 14)

# Generate sample exposure data
data = []

for strike in strikes:
    # Generate realistic exposure values
    # GEX (Gamma Exposure) - typically larger values, can be positive or negative
    gex_base = np.random.normal(0, 5000)  # Base GEX value
    gex = gex_base + np.random.normal(0, 1000)  # Add some noise
    
    # VEX (Vega Exposure) - typically smaller than GEX
    vex_base = np.random.normal(0, 500)
    vex = vex_base + np.random.normal(0, 100)
    
    # CEX (Call Exposure) - can be positive or negative
    cex_base = np.random.normal(0, 2000)
    cex = cex_base + np.random.normal(0, 300)
    
    # Create row
    row = {
        'strike': float(strike),
        'date': sample_date,
        'gex': round(gex, 2),
        'vex': round(vex, 2),
        'cex': round(cex, 2),
        # Add some traditional Greeks for fallback
        'delta': round(np.random.uniform(-1, 1), 4),
        'gamma': round(np.random.uniform(0, 0.01), 6),
        'theta': round(np.random.uniform(-50, 0), 2),
        'vega': round(np.random.uniform(0, 100), 2),
        'rho': round(np.random.uniform(-100, 100), 2),
    }
    
    data.append(row)

# Create DataFrame
df = pd.DataFrame(data)

# Sort by strike
df = df.sort_values('strike', ascending=False)

print(f"Created sample data with {len(df)} rows")
print(f"Columns: {df.columns.tolist()}")
print(f"Strike range: {df['strike'].min()} to {df['strike'].max()}")
print(f"GEX range: {df['gex'].min():.2f} to {df['gex'].max():.2f}")
print(f"VEX range: {df['vex'].min():.2f} to {df['vex'].max():.2f}")
print(f"CEX range: {df['cex'].min():.2f} to {df['cex'].max():.2f}")

print("\nFirst few rows:")
print(df.head())

# Save to parquet
output_file = 'sample_exposure_data.parquet'
df.to_parquet(output_file, index=False)
print(f"\nSaved to {output_file}")
